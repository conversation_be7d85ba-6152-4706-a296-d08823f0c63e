
using DAL.Models;
using Microsoft.EntityFrameworkCore;
using Model;
using UOW;

namespace Services
{
    public class HangBagService 
    {
        private readonly UnitOfWork _unitOfWork;

        public HangBagService(UnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public List<ListHandBagModel> GetAllHandBag()
        {
            var list = _unitOfWork.GetRepository<Handbag>()
                .Entities
                .Select(x => new ListHandBagModel
                {
                    HandbagId = x.HandbagId,
                    ModelName = x.ModelName,
                    Material = x.Material,
                    Color = x.Color,
                    Price = x.Price,
                    ReleaseDate = x.ReleaseDate,
                    Stock = x.Stock,
                    BrandName = x.Brand.BrandName,
                    Country = x.Brand.Country,
                    FoundedYear = x.Brand.FoundedYear,
                    Website = x.Brand.Website,
                })
                .ToList();

            return list;
        }

        public ListHandBagModel GetHandBagById(int id)
        {
            var handbag = _unitOfWork.GetRepository<Handbag>()
                .Entities
                .Select(x => new ListHandBagModel
                {
                    HandbagId = x.HandbagId,
                    ModelName = x.ModelName,
                    Material = x.Material,
                    Color = x.Color,
                    Price = x.Price,
                    ReleaseDate = x.ReleaseDate,
                    Stock = x.Stock,
                    BrandName = x.Brand.BrandName,
                    Country = x.Brand.Country,
                    FoundedYear = x.Brand.FoundedYear,
                    Website = x.Brand.Website,
                })
                .FirstOrDefault(x => x.HandbagId == id);

            return handbag; // Return null if not found - Controller will handle
        }

        public bool CreateHandBag(CreateHandBagModel model)
        {
            try
            {

           
            var maxId = _unitOfWork.GetRepository<Handbag>().Entities.Max(h => (int?)h.HandbagId) ?? 0;
            var handbag = new Handbag
            {
                HandbagId = maxId+1,
                ModelName = model.ModelName,
                Material = model.Material,
                Price = model.Price,
                Stock = model.Stock,
                BrandId = model.BrandId,
                ReleaseDate = DateOnly.FromDateTime(DateTime.Now)
            };
            _unitOfWork.GetRepository<Handbag>().Add(handbag);
            _unitOfWork.Save();
            return true;
            }
            catch(Exception ex)
            {
                return false;
            }
        }

        public bool UpdateHandBag(int id, UpdateHandBagModel model)
        {
            var handbag = _unitOfWork.GetRepository<Handbag>().GetById(id);
            if (handbag == null)
                return false; // Return false if not found - Controller will handle

            handbag.Material = model.Material;
            handbag.Price = model.Price;
            handbag.Stock = model.Stock;
            handbag.Color = model.Color;
            handbag.ModelName = model.ModelName;
            handbag.BrandId = model.BrandId;
            _unitOfWork.GetRepository<Handbag>().Update(handbag);
            _unitOfWork.Save();

            return true;
        }

        public bool DeleteHandBag(int id)
        {
            var handbag = _unitOfWork.GetRepository<Handbag>().GetById(id);
            if(handbag == null)
                return false; // Return false if not found - Controller will handle

            _unitOfWork.GetRepository<Handbag>().Delete(handbag);
            _unitOfWork.Save();

            return true;
        }

        public IQueryable<ListHandBagModel> SearchWithProjection(string? modelName, string? material)
        {
            var query = _unitOfWork.GetRepository<Handbag>().Entities
                .Include(h => h.Brand)
                .Select(h => new ListHandBagModel
                {
                    HandbagId = h.HandbagId,
                    ModelName = h.ModelName,
                    Material = h.Material,
                    Color = h.Color,
                    Price = h.Price,
                    Stock = h.Stock,
                    ReleaseDate = h.ReleaseDate,
                    BrandName = h.Brand.BrandName,
                    Country = h.Brand.Country,
                    FoundedYear = h.Brand.FoundedYear,
                    Website = h.Brand.Website
                });

            if (!string.IsNullOrEmpty(modelName))
            {
                query = query.Where(h => h.ModelName.Contains(modelName));
            }

            if (!string.IsNullOrEmpty(material))
            {
                query = query.Where(h => h.Material.Contains(material));
            }
            return query;
        }

    }
}
